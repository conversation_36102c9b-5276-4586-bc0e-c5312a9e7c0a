import { useAccount } from "wagmi"
import { useWriteContract } from "wagmi"
import { useEthersProvider } from "src/hooks"
import { showError, showSuccessWhenCallContract } from "src/utils/toast"
import { useTranslation } from "react-i18next"
import { CONTRACT_ADDRESS_ESTATE_TOKEN } from "src/config/env"
import { collectionAbi, estateTokenAbi } from "src/api/contracts"
import Logger from "src/utils/logger"
import { useState } from "react"

const logger = new Logger({ tag: "useWithdrawActions" })

interface UseWithdrawActionsProps {
  requestId: string
  isWithdrawNFTs: boolean
  tokenSymbol?: string
}

export function useWithdrawActions({
  requestId,
  isWithdrawNFTs,
  tokenSymbol,
}: UseWithdrawActionsProps) {
  const { t } = useTranslation()
  const { address } = useAccount()
  const { writeContractAsync } = useWriteContract()
  const ethersProvider = useEthersProvider()
  const [isLoading, setIsLoading] = useState(false)
  const [canCancelLoading, setCanCancelLoading] = useState(true)

  const onWithdrawNFTs = async () => {
    if (!ethersProvider || !address) return
    try {
      setIsLoading(true)

      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_ESTATE_TOKEN,
        abi: collectionAbi,
        functionName: "withdrawToken",
        args: [BigInt(requestId)],
      })

      setCanCancelLoading(!txHash)
      const receipt = await ethersProvider.waitForTransaction(txHash)

      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Withdraw NFT(s) success. Data will be updated in few seconds")
        )
      } else {
        throw new Error(t("Withdraw NFT(s) failed"))
      }
    } catch (e: any) {
      logger.error("Withdraw NFTs failed", e)
      showError(e.message)
    } finally {
      setIsLoading(false)
    }
  }

  const onWithdrawDeposit = async () => {
    if (!ethersProvider || !address) return
    try {
      setIsLoading(true)

      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_ESTATE_TOKEN,
        abi: estateTokenAbi,
        functionName: "withdrawDeposit",
        args: [BigInt(requestId)],
      })

      setCanCancelLoading(!txHash)
      const receipt = await ethersProvider.waitForTransaction(txHash)

      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("withdrawDepositSuccessMessage", { token: tokenSymbol })
        )
      } else {
        throw new Error(t("withdrawDepositFailed", { token: tokenSymbol }))
      }
    } catch (e: any) {
      logger.error("Withdraw tokens failed", e)
      showError(e.message)
    } finally {
      setIsLoading(false)
    }
  }

  const onWithdraw = () => {
    return isWithdrawNFTs ? onWithdrawNFTs() : onWithdrawDeposit()
  }

  return {
    onWithdraw,
    isLoading,
    setIsLoading,
    canCancelLoading,
  }
}
