import { Oracle, getOraclesV2 } from "src/api"
import { useQueryWithErrorHandling } from "src/api/query"
import QueryKeys from "src/config/queryKeys"

const useOracle = () => {
  const {
    data: oracle,
    isLoading: isInvestmentLoading,
    error: oracleError,
  } = useQueryWithErrorHandling<Oracle>({
    queryKey: [...QueryKeys.ORACLE.LIST],
    queryFn: () => getOraclesV2(),
    refetchOnMount: true,
  })

  return {
    oracle,
    isInvestmentLoading,
    oracleError,
  }
}

export default useOracle
