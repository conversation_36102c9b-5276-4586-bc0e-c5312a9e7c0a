import React, { useEffect, useRef, useState } from "react"
import { View, Text, StyleSheet, Image } from "react-native"
import { useTranslation } from "react-i18next"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import iconBrik from "assets/imagesV2/ic_brik.png"
import iconLockOpen from "assets/imagesV2/ic_lock_open.png"
import WithdrawButton from "./withdrawbutton"
import { useInvestmentContext } from "../context/InvestmentContext"
import { useAccount } from "wagmi"
import InvestmentDistributionView from "./InvestmentDistributionView"

const YourInvestments: React.FC = () => {
  const { t } = useTranslation()

  const { address } = useAccount()
  const { selectedRound, investmentRounds } = useInvestmentContext()
  const selectedInvestmentRound = investmentRounds.find(
    (round) => round.round === selectedRound
  )
  const yourInvestment = selectedInvestmentRound?.investments.find(
    (investment) => investment.address === address
  )
  const [currentUnlocked, setCurrentUnlocked] = useState(
    yourInvestment?.unlockedAmount || 0
  )

  // Calculate incrementPerSecond based on all distributions
  const calculateIncrementPerSecond = () => {
    if (!yourInvestment?.distributions) return 0

    const currentTimeInSeconds = Math.floor(Date.now() / 1000)

    return yourInvestment.distributions.reduce(
      (totalIncrement, distribution) => {
        // If current time >= vestingEndsAtInSeconds, increment is 0
        if (currentTimeInSeconds >= distribution.vestingEndsAtInSeconds) {
          return totalIncrement
        }

        // Calculate increment for this distribution: (amount - unlockedAmount) / (vestingEndsAtInSeconds - currentTime)
        const vestingTime =
          distribution.vestingEndsAtInSeconds -
          (distribution.vestingStartedAtInSeconds || 0)

        const distributionIncrement = distribution.amount / vestingTime
        return totalIncrement + distributionIncrement
      },
      0
    )
  }

  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    intervalRef.current = setInterval(() => {
      // Recalculate incrementPerSecond each time since current time changes
      const currentIncrementPerSecond = calculateIncrementPerSecond()
      setCurrentUnlocked((prev) => prev + currentIncrementPerSecond)
    }, 1000)
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current)
    }
  }, [yourInvestment])

  return (
    <View style={styles.cardContainer}>
      <View style={styles.headerContainer}>
        <Text style={[textStyles.MSemiBold, styles.headerTitle]}>
          {t("Your Investments")}
        </Text>
      </View>

      <View style={styles.investmentsListContainer}>
        {yourInvestment?.distributions.map((distribution, index) => (
          <InvestmentDistributionView
            distribution={distribution}
            index={index}
            key={index}
          />
        ))}
      </View>

      <View style={styles.currentBalanceContainer}>
        <View style={styles.currentBalanceContent}>
          <View style={styles.balanceInfoContainer}>
            <View style={styles.balanceIconAndText}>
              <View style={styles.brikIconWithLock}>
                <Image source={iconBrik} style={viewStyles.size32Icon} />
                <View style={styles.miniLockIconContainer}>
                  <Image source={iconLockOpen} style={viewStyles.size8Icon} />
                </View>
              </View>
              <View style={styles.balanceTextContainer}>
                <Text style={[textStyles.SMedium, styles.currentBalanceLabel]}>
                  {t("Current unlock balance")}
                </Text>
                <Text
                  style={[textStyles.MSemiBold, styles.currentBalanceAmount]}
                >
                  {currentUnlocked} BRIK
                </Text>
              </View>
            </View>
          </View>
          <WithdrawButton enabled={currentUnlocked > 0} />
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: Colors.Neutral950,
    borderRadius: 8,
    padding: 8,
    marginHorizontal: 16,
  },
  headerContainer: {
    marginBottom: 12,
  },
  headerTitle: {
    color: Colors.PalleteWhite,
  },
  investmentsListContainer: {
    gap: 16,
  },
  investmentItemContainer: {
    borderColor: Colors.Neutral900,
    borderWidth: 1,
    borderRadius: 6,
    padding: 8,
    justifyContent: "center",
  },
  investmentItemContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  investmentDetails: {
    gap: 8,
  },
  investmentOrderLabel: {
    color: Colors.Neutral500,
  },
  brikAmountContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  brikAmountText: {
    color: Colors.PalleteWhite,
  },
  unlockedTag: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: Colors.Success900,
    borderRadius: 6,
    paddingHorizontal: 8,
    paddingVertical: 8,
    gap: 4,
    height: 32,
  },
  unlockedTagText: {
    color: Colors.Success300,
  },
  currentBalanceContainer: {
    backgroundColor: Colors.Neutral900,
    borderColor: Colors.Neutral900,
    borderWidth: 1,
    borderRadius: 6,
    padding: 12,
  },
  currentBalanceContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  balanceInfoContainer: {},
  balanceIconAndText: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  brikIconWithLock: {
    position: "relative",
  },
  miniLockIconContainer: {
    position: "absolute",
    right: -4,
    top: 0,
    backgroundColor: Colors.Neutral950,
    borderColor: Colors.Neutral900,
    borderWidth: 1,
    borderRadius: 999,
    width: 16,
    height: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  balanceTextContainer: {
    gap: 2,
  },
  currentBalanceLabel: {
    color: Colors.Neutral500,
  },
  currentBalanceAmount: {
    color: Colors.PalleteWhite,
  },
  withdrawButton: {
    backgroundColor: Colors.Primary500,
    borderRadius: 8,
    paddingHorizontal: 16,
    height: 32,
    justifyContent: "center",
    alignItems: "center",
  },
  withdrawButtonText: {
    color: Colors.PalleteBlack,
    textAlign: "center",
  },
})

export default YourInvestments
