import React from "react"
import { View, Text, StyleSheet, Image } from "react-native"
import { useTranslation } from "react-i18next"
import { CustomPressable, SimpleLoadingView } from "src/componentsv2"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import icCircleAlert from "assets/imagesV2/ic_circle_alert.png"
import icBox from "assets/imagesV2/ic_box.png"
import { TokenizationRequestState } from "src/api/types"
import { useWithdrawData } from "./hooks/useWithdrawData"
import { useWithdrawActions } from "./hooks/useWithdrawActions"
import { useAccount } from "wagmi"
import {
  useCollectionDepositedAmount,
  useCollectionHasWithdrawn,
} from "src/api/contracts/collection"
import { useCurrencies } from "src/screensV2/shared/hooks/useCurrencies"

interface WithdrawSectionViewProps {
  requestId: string
  state: TokenizationRequestState
  currency: string
  decimals: number
  unitPrice: string
}

const WithdrawSectionView: React.FC<WithdrawSectionViewProps> = ({
  requestId,
  state,
  currency,
  decimals,
  unitPrice,
}) => {
  const { t } = useTranslation()
  const { address } = useAccount()
  const { tokenSymbol } = useCurrencies(currency)
  const depositedAmount = useCollectionDepositedAmount(
    requestId,
    address as `0x${string}`
  )
  const userHasWithdrawn = useCollectionHasWithdrawn(
    requestId,
    address as `0x${string}`
  )

  const {
    isWithdrawNFTs,
    isWithdrawDeposit,
    isCantWithdraw,
    displayDeposited,
    displayButtonText,
    displayWithdrawWarningText,
  } = useWithdrawData({
    depositedAmount,
    state,
    tokenSymbol: tokenSymbol ?? "",
    decimals,
    unitPrice,
  })

  const { onWithdraw, isLoading, setIsLoading, canCancelLoading } =
    useWithdrawActions({
      requestId: requestId,
      isWithdrawNFTs: isWithdrawNFTs,
      tokenSymbol: tokenSymbol,
    })

  if (!depositedAmount || depositedAmount <= 0 || userHasWithdrawn) {
    return null
  }

  return (
    <View style={styles.withdrawContainer}>
      <View style={styles.withdrawContent}>
        <View style={styles.myDepositedContainer}>
          <Text style={styles.myDepositedTitle}>{t("My Deposited")}</Text>
          <View style={styles.withdrawNFTAmountContainer}>
            <Image source={icBox} style={viewStyles.size12Icon} />
            <Text style={styles.withdrawNFTAmountText}>{displayDeposited}</Text>
          </View>
        </View>
        <CustomPressable
          style={[
            styles.withdrawButton,
            isCantWithdraw && styles.withdrawButtonDisabled,
          ]}
          onPress={onWithdraw}
          enabled={!isCantWithdraw}
        >
          <Text
            style={[
              styles.withdrawButtonText,
              isCantWithdraw && styles.withdrawButtonTextDisabled,
            ]}
          >
            {displayButtonText}
          </Text>
        </CustomPressable>
      </View>

      {isWithdrawDeposit && (
        <View style={styles.withdrawWarning}>
          <Image source={icCircleAlert} style={viewStyles.size12Icon} />
          <Text style={styles.withdrawWarningText}>
            {displayWithdrawWarningText}
          </Text>
        </View>
      )}

      <SimpleLoadingView
        visible={isLoading}
        onCancel={canCancelLoading ? () => setIsLoading(false) : undefined}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  withdrawContainer: {
    width: "100%",
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    borderRadius: 8,
    padding: 12,
    gap: 8,
    backgroundColor: Colors.PalleteBlack,
  },
  withdrawContent: {
    width: "100%",
    flexDirection: "row",
  },
  myDepositedContainer: {
    gap: 6,
    flex: 1.5,
  },
  myDepositedTitle: {
    color: Colors.Neutral300,
    ...textStyles.MMedium,
  },
  withdrawNFTAmountContainer: {
    flexDirection: "row",
    flex: 1,
    gap: 8,
    alignItems: "center",
  },
  withdrawNFTAmountText: {
    color: Colors.PalleteWhite,
    ...textStyles.XLMedium,
  },
  withdrawButton: {
    height: 38,
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: Colors.Primary500,
  },
  withdrawButtonDisabled: {
    backgroundColor: Colors.Neutral700,
  },
  withdrawButtonText: {
    color: Colors.PalleteBlack,
    ...textStyles.LMedium,
  },
  withdrawButtonTextDisabled: {
    color: Colors.Neutral900,
  },
  withdrawWarning: {
    flexDirection: "row",
    width: "100%",
    backgroundColor: Colors.Neutral900,
    borderRadius: 4,
    padding: 6,
    gap: 4,
  },
  withdrawWarningText: {
    color: Colors.Neutral300,
    ...textStyles.MMedium,
    paddingEnd: 6,
  },
})

export default WithdrawSectionView
