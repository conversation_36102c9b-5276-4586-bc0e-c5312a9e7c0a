import React from "react"
import { PrimaryButton } from "src/componentsv2/Button"
import { useTranslation } from "react-i18next"
import useWithdrawAll from "./useWithdraw"

interface WithdrawButtonProps {
  enabled?: boolean
}
const WithdrawButton: React.FC<WithdrawButtonProps> = ({ enabled = true }) => {
  const { t } = useTranslation()
  const { onWithdrawAll } = useWithdrawAll()
  return (
    <PrimaryButton
      title={t("Withdraw All")}
      onPress={() => onWithdrawAll()}
      height={32}
      borderRadius={8}
      enabled={enabled}
    />
  )
}

export default WithdrawButton
