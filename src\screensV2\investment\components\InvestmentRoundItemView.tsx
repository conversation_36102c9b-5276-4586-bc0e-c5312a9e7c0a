import React from "react"
import { View, Text, Image, StyleSheet } from "react-native"
import { InvestmentRound, InvestmentRoundState } from "src/api/types/oracle"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { useTranslation } from "react-i18next"
import { CircularProgress, CustomPressable } from "src/componentsv2"
import { useInvestmentContext } from "src/screensV2/investment/context/InvestmentContext"
import { useInvestmentRoundItem } from "./hooks/useInvestmentRoundItem"

const InvestmentRoundItemView: React.FC<{
  investmentRound: InvestmentRound
}> = ({ investmentRound }) => {
  const { t } = useTranslation()
  const { selectedRound, setSelectedRound, roundNameMap } =
    useInvestmentContext()
  const isSelectedRound = selectedRound === investmentRound.round
  const { stateConfig, isLive, percent, roundName } = useInvestmentRoundItem(
    investmentRound,
    roundNameMap,
    t
  )

  return (
    <CustomPressable
      enabled={investmentRound.state !== InvestmentRoundState.PENDING}
      onPress={() => {
        setSelectedRound(investmentRound.round)
      }}
      style={[
        styles.roundItem,
        stateConfig.containerStyle,
        {
          backgroundColor: isSelectedRound
            ? Colors.Success300
            : stateConfig.backgroundColor,
        },
      ]}
    >
      <View style={styles.iconAndTitle}>
        {isLive ? (
          <View style={styles.progress}>
            <CircularProgress
              percentage={percent}
              size={24}
              colorProgress={Colors.Success300}
            />
            <View style={styles.progressCenterContent}>
              <Text style={styles.percentText}>{percent}%</Text>
            </View>
          </View>
        ) : (
          <View
            style={[
              stateConfig.iconBackground,
              {
                backgroundColor: isSelectedRound
                  ? Colors.Success600
                  : Colors.Neutral950,
              },
            ]}
          >
            <Image source={stateConfig.icon} style={viewStyles.size12Icon} />
          </View>
        )}
        <View>
          <Text
            style={[
              textStyles.MSemiBold,
              {
                color: isSelectedRound
                  ? Colors.PalleteBlack
                  : stateConfig.roundNameColor,
              },
            ]}
          >
            {roundName}
          </Text>
          <Text
            style={[
              textStyles.SMedium,
              {
                color: isSelectedRound
                  ? Colors.Success800
                  : stateConfig.roundStateColor,
              },
            ]}
          >
            {stateConfig.roundStateText}
          </Text>
        </View>
      </View>
    </CustomPressable>
  )
}

const styles = StyleSheet.create({
  roundItem: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 32,
    padding: 8,
    paddingEnd: 38,
    marginHorizontal: 4,
    minWidth: 0,
    alignSelf: "flex-start",
    width: "auto",
  },
  iconAndTitle: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 24,
    gap: 12,
  },
  live: {
    borderWidth: 2,
    borderColor: Colors.Primary500,
    backgroundColor: Colors.Neutral950,
  },
  processingContainer: {
    borderWidth: 1,
    borderColor: Colors.Neutral900,
    backgroundColor: Colors.PalleteBlack,
  },
  doneBackgroud: {
    backgroundColor: Colors.Neutral900,
    padding: 6,
    borderRadius: 999,
  },
  lockBackgroud: {
    backgroundColor: Colors.Neutral950,
    padding: 6,
    borderRadius: 999,
  },
  progressCenterContent: {
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
    gap: 4,
  },
  percentText: {
    ...textStyles.size2XSBold,
    color: Colors.Success300,
  },
  progress: {
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 999,
    backgroundColor: Colors.Neutral950,
  },
})

export default InvestmentRoundItemView
