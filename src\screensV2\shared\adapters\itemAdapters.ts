import {
  Application,
  ApplicationStatus,
  MyEstate,
  MyTokenization,
} from "src/api"
import { ListItemCardProps } from "../components/ListItemCard"
import * as Routes from "src/navigatorV2/routes/RoutesV2"

/**
 * Adapts an Application object to the common ListItemCardProps format
 */
export const applicationToListItem = (
  application: Application,
  onNavigate?: (route: string, params: any) => void
): ListItemCardProps => {
  return {
    name: application.metadata?.metadata?.name,
    imageUrl: application.metadata?.imageUrl,
    status: application.metadata?.metadata?.status,
    price: application.unitPrice,
    decimals: application.decimals,
    currency: application.currency,
    totalSupply: application.totalSupply,
    onPress: onNavigate
      ? () =>
          onNavigate(Routes.APPLICATION_DETAIL, {
            applicationId: application.id,
          })
      : undefined,
  }
}

/**
 * Adapts a TokenizationRequest object to the common ListItemCardProps format
 */
export const tokenizationRequestToListItem = (
  tokenizationRequest: MyTokenization,
  onNavigate?: (route: string, params: any) => void
): ListItemCardProps => {
  return {
    name: tokenizationRequest.name,
    imageUrl: tokenizationRequest.imageUrl,
    status: tokenizationRequest.status,
    price: tokenizationRequest.initialPrice,
    decimals: tokenizationRequest.decimals,
    currency: tokenizationRequest.currency,
    soldAmount: tokenizationRequest.soldAmount,
    maxSellingAmount: tokenizationRequest.maxSellingAmount,
    onPress: !onNavigate
      ? undefined
      : tokenizationRequest.requestId && tokenizationRequest.requestId !== "0"
        ? () =>
            onNavigate(Routes.ESTATE_REQUEST_DETAIL, {
              estateRequestId: tokenizationRequest.requestId,
            })
        : tokenizationRequest.applicationId &&
            tokenizationRequest.applicationId !== 0
          ? () =>
              onNavigate(Routes.APPLICATION_DETAIL, {
                applicationId: tokenizationRequest.applicationId,
              })
          : undefined,
  }
}

/**
 * Adapts an Estate object to the common ListItemCardProps format
 */
export const estateToListItem = (
  estate: MyEstate,
  onNavigate?: (route: string, params: any) => void
): ListItemCardProps => {
  return {
    name: estate.name,
    imageUrl: estate.imageUrl,
    price: estate.initialUnitPrice,
    status: ApplicationStatus.REQUEST_CONFIRMED,
    decimals: estate.decimals,
    currency: estate.currency,
    totalSupply: estate.totalSupply,
    onPress: onNavigate
      ? () =>
          onNavigate(Routes.ESTATE_DETAIL, {
            estateId: estate.id,
          })
      : undefined,
  }
}
