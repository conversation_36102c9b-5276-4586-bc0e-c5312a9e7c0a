import React, { useCallback } from "react"
import { StyleSheet, View, FlatList, ListRenderItem } from "react-native"
import { Background, EmptyView, LoadingView } from "components"
import { useEstateRequestDetailContext } from "./context"
import { useEstateRequestSections } from "./hooks"
import { EstateRequestDetailSectionItem } from "./types"
import { SectionRenderer } from "./components"
import { EstateDetailHeader } from "../shared/components"

// Component to render the content of the screen
const EstateRequestDetailContent: React.FC = () => {
  const { estateRequestDetail, isLoadingEstateRequestDetail, refreshAll } =
    useEstateRequestDetailContext()
  const sections = useEstateRequestSections()

  // Render each section
  const renderItem: ListRenderItem<EstateRequestDetailSectionItem> =
    useCallback(({ item }) => <SectionRenderer item={item} />, [])

  // Key extractor for the FlatList
  const keyExtractor = useCallback(
    (item: EstateRequestDetailSectionItem) => item.id,
    []
  )

  // Show loading state
  if (isLoadingEstateRequestDetail) {
    return <LoadingView />
  }

  // Show empty state if no data
  if (!estateRequestDetail) {
    return <EmptyView />
  }

  // Show estate request details with FlatList
  return (
    <FlatList
      data={sections}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.contentContainer}
      onRefresh={refreshAll}
      refreshing={isLoadingEstateRequestDetail}
    />
  )
}

// The main screen component that provides the context
const EstateRequestDetailView: React.FC = () => {
  const { estateRequestDetail } = useEstateRequestDetailContext()

  return (
    <Background>
      <View style={styles.container}>
        <EstateDetailHeader
          status={estateRequestDetail?.metadata?.metadata?.status}
        />
        <EstateRequestDetailContent />
      </View>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 50,
  },
})

export default EstateRequestDetailView
